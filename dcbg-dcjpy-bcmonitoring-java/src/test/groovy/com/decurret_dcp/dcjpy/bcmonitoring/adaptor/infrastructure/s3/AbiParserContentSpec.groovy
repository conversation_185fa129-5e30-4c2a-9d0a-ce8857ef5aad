package com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3

import com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser
import com.decurret_dcp.dcjpy.bcmonitoring.config.BcmonitoringConfigurationProperties
import com.decurret_dcp.dcjpy.bcmonitoring.domain.model.ContractEvents
import java.nio.charset.StandardCharsets
import org.web3j.abi.TypeReference
import org.web3j.abi.datatypes.Event
import org.web3j.protocol.core.methods.response.Log
import spock.lang.Specification

class AbiParserContentSpec extends Specification {

	AbiParser parser
	BcmonitoringConfigurationProperties propertiesMock

	def setup() {
		propertiesMock = Mock(BcmonitoringConfigurationProperties)
		parser = new AbiParser(propertiesMock)
	}

	// clean up the contractEventStore and contractAddresses after each test
	def cleanup() {
		parser.contractEventStore.clear()
		parser.contractAddresses.clear()
	}

	def "parseAbiContent should parse Truffle format ABI"() {
		given: "A Truffle format ABI JSON"
		def truffleAbi = '''{
            "contractName": "TestContract",
            "networks": {
                "1": {
                    "address": "******************************************"
                }
            },
            "abi": [
                {
                    "type": "event",
                    "name": "Transfer",
                    "inputs": [
                        {"indexed": true, "name": "from", "type": "address"},
                        {"indexed": true, "name": "to", "type": "address"},
                        {"indexed": false, "name": "value", "type": "uint256"}
                    ]
                }
            ]
        }'''
		def inputStream = new ByteArrayInputStream(truffleAbi.getBytes(StandardCharsets.UTF_8))
		def objectKey = "3000/TestContract.json"
		def lastModified = new Date()

		// Set environment variable for testing
		propertiesMock.getAbiFormat() >> "truffle"

		when: "Parsing the ABI content"
		def result = parser.parseAbiContent(inputStream, objectKey, lastModified)

		then: "Contract info should be correctly extracted"
		result != null
		result.address == "******************************************"
		result.name == "TestContract"
		result.lastModified == lastModified

		and: "Events should be parsed"
		parser.contractEventStore.size() > 0
		parser.contractAddresses.size() > 0
	}

	def "parseAbiContent should parse Hardhat format ABI"() {
		given: "A Hardhat format ABI JSON"
		def hardhatAbi = '''{
            "address": "******************************************",
            "abi": [
                {
                    "type": "event",
                    "name": "Approval",
                    "inputs": [
                        {"indexed": true, "name": "owner", "type": "address"},
                        {"indexed": true, "name": "spender", "type": "address"},
                        {"indexed": false, "name": "value", "type": "uint256"}
                    ]
                }
            ]
        }'''
		def inputStream = new ByteArrayInputStream(hardhatAbi.getBytes(StandardCharsets.UTF_8))
		def objectKey = "3001/TokenContract.json"
		def lastModified = new Date()

		// Mock the abiFormat to return a non-Truffle format
		propertiesMock.getAbiFormat() >> "hardhat"

		when: "Parsing the ABI content"
		def result = parser.parseAbiContent(inputStream, objectKey, lastModified)

		then: "Contract info should be correctly extracted"
		result != null
		result.address == "******************************************"
		result.name == "TokenContract"
		result.lastModified == lastModified

		and: "Events should be parsed"
		parser.contractEventStore.size() == 1
		parser.contractAddresses.size() == 1
	}

	def "parseAbiContent should handle missing ABI section"() {
		given: "A JSON without ABI section"
		def invalidAbi = '''{
            "address": "******************************************",
            "something": "else"
        }'''
		def inputStream = new ByteArrayInputStream(invalidAbi.getBytes(StandardCharsets.UTF_8))
		def objectKey = "3000/TestContract.json"
		def lastModified = new Date()

		when: "Parsing the ABI content"
		parser.parseAbiContent(inputStream, objectKey, lastModified)

		then: "An IOException should be thrown"
		thrown(IOException)
	}

	def "parseAbiContent should handle invalid JSON"() {
		given: "Invalid JSON content"
		def invalidJson = "{ not valid json }"
		def inputStream = new ByteArrayInputStream(invalidJson.getBytes(StandardCharsets.UTF_8))
		def objectKey = "3000/TestContract.json"
		def lastModified = new Date()

		when: "Parsing the ABI content"
		parser.parseAbiContent(inputStream, objectKey, lastModified)

		then: "An IOException should be thrown"
		thrown(IOException)
	}

	def "parseAbi should handle empty ABI JSON"() {
		given:
		String emptyAbi = "[]"

		when:
		parser.parseAbi(emptyAbi)

		then:

		parser.contractEventStore.isEmpty()
		parser.contractAddresses.isEmpty()
	}

	def "parseAbi should skip entries with invalid event types"() {
		given:
		String abiWithInvalidType = '''[
        {
            "type": "invalid",
            "name": "InvalidEvent",
            "inputs": [
                {"indexed": true, "name": "data", "type": "address"}
            ]
        }
    ]'''

		when:
		parser.parseAbi(abiWithInvalidType)

		then:
		parser.contractEventStore.isEmpty()
		parser.contractAddresses.isEmpty()
	}

	def "parseAbi should handle events with missing name"() {
		given:
		String abiWithMissingName = '''[
        {
            "type": "event",
            "inputs": [
                {"indexed": true, "name": "from", "type": "address"},
                {"indexed": true, "name": "to", "type": "address"}
            ]
        }
    ]'''

		when:
		parser.parseAbi(abiWithMissingName)

		then:
		parser.contractEventStore.isEmpty()
		parser.contractAddresses.isEmpty()
	}

	def "parseAbi should handle events with empty inputs"() {
		given:
		String abiWithEmptyInputs = '''[
        {
            "type": "event",
            "name": "EmptyInputsEvent",
            "inputs": []
        }
    ]'''

		when:
		def result = parser.parseAbi(abiWithEmptyInputs)

		then:
		result.size() == 1
		result.iterator().next().getValue().getEvent().getName().equals("EmptyInputsEvent")
	}

	def "parseAbi should handle events with duplicate signatures"() {
		given:
		String abiWithDuplicateSignatures = '''[
        {
            "type": "event",
            "name": "DuplicateEvent",
            "inputs": [
                {"indexed": true, "name": "from", "type": "address"}
            ]
        },
        {
            "type": "event",
            "name": "DuplicateEvent",
            "inputs": [
                {"indexed": true, "name": "from", "type": "address"}
            ]
        }
    ]'''

		when:
		def result = parser.parseAbi(abiWithDuplicateSignatures)

		then:
		result.size() == 1
		result.iterator().next().getValue().getEvent().getName().equals("DuplicateEvent")
	}

	def "parseAbi should handle mixed case Solidity types"() {
		given:
		String abiWithMixedCaseTypes = '''[
        {
            "type": "event",
            "name": "MixedCaseEvent",
            "inputs": [
                {"indexed": true, "name": "from", "type": "address"},
                {"indexed": false, "name": "value", "type": "uint256"}
            ]
        }
    ]'''

		when:
		def result = parser.parseAbi(abiWithMixedCaseTypes)

		then:
		result.size() == 1
		result.iterator().next().getValue().getEvent().getName().equals("MixedCaseEvent")
	}

	def "parseAbi should handle a large number of events"() {
		given:
		StringBuilder largeAbi = new StringBuilder("[")
		(1..1000).each { i ->
			largeAbi.append("""
        {
            "type": "event",
            "name": "Event$i",
            "inputs": [
                {"indexed": true, "name": "from", "type": "address"},
                {"indexed": false, "name": "value", "type": "uint256"}
            ]
        }""")
			if (i < 1000) {
				largeAbi.append(",")
			}
		}
		largeAbi.append("]")

		when:
		def result = parser.parseAbi(largeAbi.toString())

		then:

		result.size() == 1000
	}

	def "parseAbi should handle null or empty ABI content"() {
		when: "Parsing null ABI content"
		parser.parseAbi(null)

		then: "No events should be parsed"
		parser.contractEventStore.isEmpty()
		parser.contractAddresses.isEmpty()

		when: "Parsing empty ABI content"
		parser.parseAbi("")

		then: "No events should be parsed"
		parser.contractEventStore.isEmpty()
		parser.contractAddresses.isEmpty()
	}

	def "parseAbi should handle invalid JSON"() {
		given: "Invalid JSON content"
		String invalidJson = "{ invalid json }"

		when: "Parsing the invalid JSON"
		parser.parseAbi(invalidJson)

		then: "An exception should be thrown"
		thrown(IOException)
	}

	def "parseAbi should handle events with unsupported types"() {
		given: "ABI with unsupported type"
		String abiWithUnsupportedType = '''[
            {
                "type": "event",
                "name": "UnsupportedTypeEvent",
                "inputs": [
                    {"indexed": true, "name": "data", "type": "unsupportedType"}
                ]
            }
        ]'''

		when: "Parsing the ABI"
		parser.parseAbi(abiWithUnsupportedType)

		then: "An IOException should be thrown"
		thrown(IOException)
	}

	def "parseAbiContent should handle missing address in networks"() {
		given: "Truffle ABI with missing address"
		def truffleAbi = '''{
            "contractName": "TestContract",
            "networks": {
                "1": {}
            },
            "abi": []
        }'''
		def inputStream = new ByteArrayInputStream(truffleAbi.getBytes(StandardCharsets.UTF_8))
		def objectKey = "3000/TestContract.json"
		def lastModified = new Date()

		propertiesMock.getAbiFormat() >> "truffle"

		when: "Parsing the ABI content"
		def result = parser.parseAbiContent(inputStream, objectKey, lastModified)

		then: "Contract info should have an empty address"
		result.address == ""
		result.name == "TestContract"
		result.lastModified == lastModified
	}

	def "parseAbiContent should handle missing contract name in object key"() {
		given: "ABI JSON with missing contract name"
		def abiJson = '''{
            "address": "******************************************",
            "abi": []
        }'''
		def inputStream = new ByteArrayInputStream(abiJson.getBytes(StandardCharsets.UTF_8))
		def objectKey = "3000/.json"
		def lastModified = new Date()

		propertiesMock.getAbiFormat() >> "hardhat"

		when: "Parsing the ABI content"
		def result = parser.parseAbiContent(inputStream, objectKey, lastModified)

		then: "Contract info should have an empty name"
		result.address == "******************************************"
		result.name == ""
		result.lastModified == lastModified
	}



	def "parseAbiContent should close input stream after parsing"() {
		given: "A valid ABI JSON"
		def abiJson = '''{
            "address": "******************************************",
            "abi": []
        }'''
		def inputStream = Mock(InputStream)
		inputStream.readAllBytes() >> abiJson.getBytes(StandardCharsets.UTF_8)

		when: "Parsing the ABI content"
		parser.parseAbiContent(inputStream, "3000/TestContract.json", new Date())

		then: "The input stream should be closed"
		1 * inputStream.close()
	}

	def "parseAbi should skip events with missing or empty names"() {
		given: "ABI JSON with events missing or having empty names"
		String abiWithMissingOrEmptyNames = '''[
        {
            "type": "event",
            "inputs": [
                {"indexed": true, "name": "from", "type": "address"}
            ]
        },
        {
            "type": "event",
            "name": "",
            "inputs": [
                {"indexed": true, "name": "to", "type": "address"}
            ]
        },
        {
            "type": "event",
            "name": "1",
            "inputs": [
                {"indexed": "true", "name": "to", "type": "string"}
            ]
        },
        {
            "type": "event",
            "name": "1.2",
            "inputs": [
                {"indexed": "true", "name": "to", "type": "bool"}
            ]
        },
        {
            "type": "event",
            "name": "1.3",
            "inputs": [
                {"indexed": "true", "name": "to", "type": "bytes"}
            ]
        },
        {
            "type": "event",
            "name": "1.4",
            "inputs": [
                {"indexed": "true", "name": "to", "type": "bytes32"}
            ]
        },
        {
            "type": "event",
            "name": "2",
            "inputs": [
                {"name": "to", "type": "address"}
            ]
        }
    ]'''

		when: "Parsing the ABI"
		def result = parser.parseAbi(abiWithMissingOrEmptyNames)

		then: "No events should be parsed"
		result.size() == 5
	}

	def "parseAbiContent should handle networksNode not being an object"() {
		given: "ABI JSON with networksNode as a non-object"
		def abiJson = '''{
        "contractName": "TestContract",
        "networks": "invalidNetworks",
        "abi": []
    }'''
		def inputStream = new ByteArrayInputStream(abiJson.getBytes(StandardCharsets.UTF_8))
		def objectKey = "3000/TestContract.json"
		def lastModified = new Date()

		propertiesMock.getAbiFormat() >> "truffle"

		when: "Parsing the ABI content"
		def result = parser.parseAbiContent(inputStream, objectKey, lastModified)

		then: "Contract info should have an empty address"
		result.address == ""
		result.name == "TestContract"
		result.lastModified == lastModified
	}

	def "appendContractAddress should add new addresses only once"() {
		given: "A contract address"
		def address = "******************************************"

		when: "Adding the address for the first time"
		parser.appendContractAddress(address)

		then: "The address should be added to the list"
		parser.contractAddresses.contains(address)
		parser.contractAddresses.size() == 1

		when: "Adding the same address again"
		parser.appendContractAddress(address)

		then: "The address should not be added again"
		parser.contractAddresses.contains(address)
		parser.contractAddresses.size() == 1

		when: "Adding a different address"
		def address2 = "0x0987654321098765432109876543210987654321"
		parser.appendContractAddress(address2)

		then: "Both addresses should be in the list"
		parser.contractAddresses.contains(address)
		parser.contractAddresses.contains(address2)
		parser.contractAddresses.size() == 2
	}

	def "parseAbi should handle events with null inputs"() {
		given: "ABI JSON with event having null inputs"
		def abiJson = '''[
			{
				"type": "event",
				"name": "EventWithNullInputs",
				"inputs": null
			}
		]'''

		when: "Parsing the ABI"
		def result = parser.parseAbi(abiJson)

		then: "No events should be parsed due to null inputs"
		result.isEmpty()
	}

	def "parseAbi should handle events with empty name"() {
		given: "ABI JSON with event having empty name"
		def abiJson = '''[
			{
				"type": "event",
				"name": "",
				"inputs": []
			}
		]'''

		when: "Parsing the ABI"
		def result = parser.parseAbi(abiJson)

		then: "No events should be parsed due to empty name"
		result.isEmpty()
	}

	def "parseAbi should handle events with null name"() {
		given: "ABI JSON with event having null name"
		def abiJson = '''[
			{
				"type": "event",
				"name": null,
				"inputs": []
			}
		]'''

		when: "Parsing the ABI"
		def result = parser.parseAbi(abiJson)

		then: "No events should be parsed due to null name"
		result.isEmpty()
	}

	def "parseAbi should handle different Solidity types correctly"() {
		given: "ABI JSON with various Solidity types"
		def abiJson = '''[
			{
				"type": "event",
				"name": "MultiTypeEvent",
				"inputs": [
					{"name": "uintParam", "type": "uint256", "indexed": true},
					{"name": "addressParam", "type": "address", "indexed": true},
					{"name": "stringParam", "type": "string", "indexed": false},
					{"name": "boolParam", "type": "bool", "indexed": false},
					{"name": "bytes32Param", "type": "bytes32", "indexed": true},
					{"name": "bytesParam", "type": "bytes", "indexed": false}
				]
			}
		]'''

		when: "Parsing the ABI"
		def result = parser.parseAbi(abiJson)

		then: "Event should be parsed with all parameter types"
		result.size() == 1
		def contractAbiEvent = result.values().iterator().next()
		contractAbiEvent.getInputs().size() == 6

		// Verify all parameter types are handled
		def inputs = contractAbiEvent.getInputs()
		inputs[0].getName() == "uintParam"
		inputs[0].isIndexed() == true
		inputs[1].getName() == "addressParam"
		inputs[1].isIndexed() == true
		inputs[2].getName() == "stringParam"
		inputs[2].isIndexed() == false
		inputs[3].getName() == "boolParam"
		inputs[3].isIndexed() == false
		inputs[4].getName() == "bytes32Param"
		inputs[4].isIndexed() == true
		inputs[5].getName() == "bytesParam"
		inputs[5].isIndexed() == false
	}

	def "parseAbiContent should handle missing ABI section"() {
		given: "JSON without ABI section"
		def jsonWithoutAbi = '''{
			"contractName": "TestContract",
			"address": "******************************************"
		}'''
		def inputStream = new ByteArrayInputStream(jsonWithoutAbi.getBytes(StandardCharsets.UTF_8))
		def objectKey = "3000/TestContract.json"
		def lastModified = new Date()

		propertiesMock.getAbiFormat() >> "hardhat"

		when: "Parsing the ABI content"
		parser.parseAbiContent(inputStream, objectKey, lastModified)

		then: "IOException should be thrown"
		def e = thrown(IOException)
		e.message == "ABI section not found in JSON"
	}

	def "parseAbiContent should handle non-truffle format"() {
		given: "Hardhat format ABI"
		def hardhatAbi = '''{
			"address": "******************************************",
			"abi": [
				{
					"type": "event",
					"name": "TestEvent",
					"inputs": []
				}
			]
		}'''
		def inputStream = new ByteArrayInputStream(hardhatAbi.getBytes(StandardCharsets.UTF_8))
		def objectKey = "3000/TestContract.json"
		def lastModified = new Date()

		propertiesMock.getAbiFormat() >> "hardhat"

		when: "Parsing the ABI content"
		def result = parser.parseAbiContent(inputStream, objectKey, lastModified)

		then: "Contract info should be created correctly"
		result.address == "******************************************"
		result.name == "TestContract"
		result.lastModified == lastModified
	}



	def "parseAbiContent should handle truffle format with multiple networks"() {
		given: "Truffle ABI with multiple networks"
		def truffleAbi = '''{
			"contractName": "TestContract",
			"networks": {
				"1": {
					"address": "0x1111111111111111111111111111111111111111"
				},
				"3": {
					"address": "0x3333333333333333333333333333333333333333"
				}
			},
			"abi": []
		}'''
		def inputStream = new ByteArrayInputStream(truffleAbi.getBytes(StandardCharsets.UTF_8))
		def objectKey = "3000/TestContract.json"
		def lastModified = new Date()

		propertiesMock.getAbiFormat() >> "truffle"

		when: "Parsing the ABI content"
		def result = parser.parseAbiContent(inputStream, objectKey, lastModified)

		then: "Should use the first address found"
		result.address == "0x1111111111111111111111111111111111111111"
		result.name == "TestContract"
		result.lastModified == lastModified
	}

	def "parseAndRegisterEvents should register events correctly"() {
		given: "Contract details and ABI JSON"
		def address = "******************************************"
		def contractName = "TestContract"
		def abiJson = '''[
			{
				"type": "event",
				"name": "TestEvent",
				"inputs": []
			}
		]'''

		when: "Registering events"
		parser.parseAndRegisterEvents(address, contractName, abiJson)

		then: "Events should be registered in the store"
		parser.contractEventStore.containsKey(address)
		parser.contractEventStore.get(address).contractName == contractName
		parser.contractEventStore.get(address).events.size() == 1
	}

	def "getABIEventByLog should find and return event for valid log"() {
		given: "A log with valid address and event signature"
		def eventSignature = "0x1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef"
		def contractAddress = "******************************************"

		// Create a real Event instance
		List<TypeReference<?>> parameters = []
		def event = new Event("TestEvent", parameters)
		def contractAbiEvent = new AbiParser.ContractAbiEvent(event, [])

		Log log = new Log()
		log.topics = [eventSignature]
		log.address = contractAddress

		and: "The contract events are in the store"
		def eventsMap = [(eventSignature.toLowerCase()): contractAbiEvent]
		parser.contractEventStore.put(contractAddress.toLowerCase(),
				ContractEvents.builder().contractName("TestContract").events(eventsMap).build())

		when: "Getting the event by log"
		def result = parser.getABIEventByLog(log)

		then: "The correct event should be returned"
		result == event

		cleanup:
		parser.contractEventStore.remove(contractAddress.toLowerCase())
	}

	def "getABIEventByLog should throw exception when contract address not found"() {
		given: "A log with address not in the store"
		def eventSignature = "0x1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef"
		def contractAddress = "0xabcdef1234567890123456789012345678901234"

		Log log = new Log()
		log.topics = [eventSignature]
		log.address = contractAddress

		and: "The contract address is not in the store"
		assert !parser.contractEventStore.containsKey(contractAddress.toLowerCase())

		when: "Getting the event by log"
		def result = parser.getABIEventByLog(log)

		then: "Null should be returned"
		result == null
	}

	def "getABIEventByLog should throw exception when event signature not found"() {
		given: "A log with valid address but unknown event signature"
		def knownSignature = "0x1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef"
		def unknownSignature = "0xdeadbeefdeadbeefdeadbeefdeadbeefdeadbeefdeadbeefdeadbeefdeadbeef"
		def contractAddress = "******************************************"

		// Create a real Event instance
		List<TypeReference<?>> parameters = []
		def event = new Event("TestEvent", parameters)
		def contractAbiEvent = new AbiParser.ContractAbiEvent(event, [])

		Log log = new Log()
		log.topics = [unknownSignature]
		log.address = contractAddress

		and: "The contract events are in the store but without the requested signature"
		def eventsMap = [(knownSignature.toLowerCase()): contractAbiEvent]
		parser.contractEventStore.put(contractAddress.toLowerCase(),
				ContractEvents.builder().contractName("TestContract").events(eventsMap).build())

		when: "Getting the event by log"
		def result = parser.getABIEventByLog(log)

		then: "Null should be returned"
		result == null

		cleanup:
		parser.contractEventStore.remove(contractAddress.toLowerCase())
	}

	def "getABIEventByLog should handle case-insensitive matching"() {
		given: "A log with uppercase address and event signature"
		def eventSignature = "0x1234567890ABCDEF1234567890ABCDEF1234567890ABCDEF1234567890ABCDEF"
		def contractAddress = "******************************************"

		// Create a real Event instance
		List<TypeReference<?>> parameters = []
		def event = new Event("TestEvent", parameters)
		def contractAbiEvent = new AbiParser.ContractAbiEvent(event, [])

		Log log = new Log()
		log.topics = [eventSignature]
		log.address = contractAddress.toUpperCase()

		and: "The contract events are in the store with lowercase keys"
		def eventsMap = [(eventSignature.toLowerCase()): contractAbiEvent]
		parser.contractEventStore.put(contractAddress.toLowerCase(),
				ContractEvents.builder().contractName("TestContract").events(eventsMap).build())

		when: "Getting the event by log"
		def result = parser.getABIEventByLog(log)

		then: "The correct event should be returned despite case differences"
		result == event

		cleanup:
		parser.contractEventStore.remove(contractAddress.toLowerCase())
	}

	def "getContractAbiEventByLog should find and return contract ABI event for valid log"() {
		given: "A log with valid address and event signature"
		def eventSignature = "0x1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef"
		def contractAddress = "******************************************"

		// Create a real Event instance and ContractAbiEvent
		List<TypeReference<?>> parameters = []
		def event = new Event("TestEvent", parameters)
		def contractAbiEvent = new AbiParser.ContractAbiEvent(event, [])

		Log log = new Log()
		log.topics = [eventSignature]
		log.address = contractAddress

		and: "The contract events are in the store"
		def eventsMap = [(eventSignature.toLowerCase()): contractAbiEvent]
		parser.contractEventStore.put(contractAddress.toLowerCase(),
				ContractEvents.builder().contractName("TestContract").events(eventsMap).build())

		when: "Getting the contract ABI event by log"
		def result = parser.getContractAbiEventByLog(log)

		then: "The correct contract ABI event should be returned"
		result == contractAbiEvent
		result.getEvent() == event

		cleanup:
		parser.contractEventStore.remove(contractAddress.toLowerCase())
	}

	def "getContractAbiEventByLog should return null when contract address not found"() {
		given: "A log with unknown contract address"
		def eventSignature = "0x1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef"
		def unknownAddress = "0x9999999999999999999999999999999999999999"

		Log log = new Log()
		log.topics = [eventSignature]
		log.address = unknownAddress

		when: "Getting the contract ABI event by log"
		def result = parser.getContractAbiEventByLog(log)

		then: "Null should be returned"
		result == null
	}

	def "getContractAbiEventByLog should return null when event signature not found"() {
		given: "A log with known address but unknown event signature"
		def knownSignature = "0x1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef"
		def unknownSignature = "0x9999999999999999999999999999999999999999999999999999999999999999"
		def contractAddress = "******************************************"

		// Create a real Event instance and ContractAbiEvent
		List<TypeReference<?>> parameters = []
		def event = new Event("TestEvent", parameters)
		def contractAbiEvent = new AbiParser.ContractAbiEvent(event, [])

		Log log = new Log()
		log.topics = [unknownSignature]
		log.address = contractAddress

		and: "The contract events are in the store but without the requested signature"
		def eventsMap = [(knownSignature.toLowerCase()): contractAbiEvent]
		parser.contractEventStore.put(contractAddress.toLowerCase(),
				ContractEvents.builder().contractName("TestContract").events(eventsMap).build())

		when: "Getting the contract ABI event by log"
		def result = parser.getContractAbiEventByLog(log)

		then: "Null should be returned"
		result == null

		cleanup:
		parser.contractEventStore.remove(contractAddress.toLowerCase())
	}

	def "getContractAbiEventByLog should handle case-insensitive matching"() {
		given: "A log with uppercase address and event signature"
		def eventSignature = "0x1234567890ABCDEF1234567890ABCDEF1234567890ABCDEF1234567890ABCDEF"
		def contractAddress = "******************************************"

		// Create a real Event instance and ContractAbiEvent
		List<TypeReference<?>> parameters = []
		def event = new Event("TestEvent", parameters)
		def contractAbiEvent = new AbiParser.ContractAbiEvent(event, [])

		Log log = new Log()
		log.topics = [eventSignature.toUpperCase()]
		log.address = contractAddress.toUpperCase()

		and: "The contract events are in the store with lowercase keys"
		def eventsMap = [(eventSignature.toLowerCase()): contractAbiEvent]
		parser.contractEventStore.put(contractAddress.toLowerCase(),
				ContractEvents.builder().contractName("TestContract").events(eventsMap).build())

		when: "Getting the contract ABI event by log"
		def result = parser.getContractAbiEventByLog(log)

		then: "The correct contract ABI event should be returned despite case differences"
		result == contractAbiEvent
		result.getEvent() == event

		cleanup:
		parser.contractEventStore.remove(contractAddress.toLowerCase())
	}

	def "parseAbi should handle tuple types with components"() {
		given: "ABI JSON with tuple type event"
		def abiJson = '''[
			{
				"type": "event",
				"name": "TupleEvent",
				"inputs": [
					{
						"indexed": false,
						"name": "tupleParam",
						"type": "tuple",
						"components": [
							{"name": "field1", "type": "uint256"},
							{"name": "field2", "type": "address"}
						]
					}
				]
			}
		]'''

		when: "Parsing the ABI"
		def result = parser.parseAbi(abiJson)

		then: "Event with tuple should be parsed correctly"
		result.size() == 1
		def contractAbiEvent = result.values().iterator().next()
		contractAbiEvent.getEvent().getName() == "TupleEvent"
		contractAbiEvent.getInputs().size() == 1
		contractAbiEvent.getInputs()[0].isTuple() == true
		contractAbiEvent.getInputs()[0].getComponents().size() == 2
	}

	def "parseAbi should handle nested tuple types"() {
		given: "ABI JSON with nested tuple type event"
		def abiJson = '''[
			{
				"type": "event",
				"name": "NestedTupleEvent",
				"inputs": [
					{
						"indexed": false,
						"name": "nestedTuple",
						"type": "tuple",
						"components": [
							{"name": "field1", "type": "uint256"},
							{
								"name": "innerTuple",
								"type": "tuple",
								"components": [
									{"name": "innerField1", "type": "address"},
									{"name": "innerField2", "type": "bool"}
								]
							}
						]
					}
				]
			}
		]'''

		when: "Parsing the ABI"
		def result = parser.parseAbi(abiJson)

		then: "Nested tuple should be parsed correctly"
		result.size() == 1
		def contractAbiEvent = result.values().iterator().next()
		contractAbiEvent.getEvent().getName() == "NestedTupleEvent"
		def tupleInput = contractAbiEvent.getInputs()[0]
		tupleInput.isTuple() == true
		tupleInput.getComponents().size() == 2
		tupleInput.getComponents()[1].isTuple() == true
		tupleInput.getComponents()[1].getComponents().size() == 2
	}

	def "parseAbi should handle tuple array types"() {
		given: "ABI JSON with tuple array type event"
		def abiJson = '''[
			{
				"type": "event",
				"name": "TupleArrayEvent",
				"inputs": [
					{
						"indexed": false,
						"name": "tupleArrayParam",
						"type": "tuple[]",
						"components": [
							{"name": "field1", "type": "uint256"},
							{"name": "field2", "type": "string"}
						]
					}
				]
			}
		]'''

		when: "Parsing the ABI"
		def result = parser.parseAbi(abiJson)

		then: "Tuple array should be parsed correctly"
		result.size() == 1
		def contractAbiEvent = result.values().iterator().next()
		contractAbiEvent.getEvent().getName() == "TupleArrayEvent"
		def tupleInput = contractAbiEvent.getInputs()[0]
		tupleInput.isTuple() == true
		tupleInput.getType() == "tuple[]"
		tupleInput.getComponents().size() == 2
	}

	def "parseAbi should handle tuple with empty components"() {
		given: "ABI JSON with tuple having empty components"
		def abiJson = '''[
			{
				"type": "event",
				"name": "EmptyTupleEvent",
				"inputs": [
					{
						"indexed": false,
						"name": "emptyTuple",
						"type": "tuple",
						"components": []
					}
				]
			}
		]'''

		when: "Parsing the ABI"
		def result = parser.parseAbi(abiJson)

		then: "Tuple with empty components should be parsed"
		result.size() == 1
		def contractAbiEvent = result.values().iterator().next()
		contractAbiEvent.getEvent().getName() == "EmptyTupleEvent"
		def tupleInput = contractAbiEvent.getInputs()[0]
		tupleInput.isTuple() == true
		tupleInput.getComponents().isEmpty()
	}

	def "parseAbi should handle tuple with null components"() {
		given: "ABI JSON with tuple having null components"
		def abiJson = '''[
			{
				"type": "event",
				"name": "NullComponentsEvent",
				"inputs": [
					{
						"indexed": false,
						"name": "nullTuple",
						"type": "tuple",
						"components": null
					}
				]
			}
		]'''

		when: "Parsing the ABI"
		parser.parseAbi(abiJson)

		then: "Should throw IOException due to unsupported type"
		thrown(IOException)
	}

	def "parseAbi should handle tuple with missing components field"() {
		given: "ABI JSON with tuple missing components field"
		def abiJson = '''[
			{
				"type": "event",
				"name": "MissingComponentsEvent",
				"inputs": [
					{
						"indexed": false,
						"name": "missingTuple",
						"type": "tuple"
					}
				]
			}
		]'''

		when: "Parsing the ABI"
		def result = parser.parseAbi(abiJson)

		then: "Tuple with missing components should be handled gracefully"
		result.size() == 1
		def contractAbiEvent = result.values().iterator().next()
		contractAbiEvent.getEvent().getName() == "MissingComponentsEvent"
		def tupleInput = contractAbiEvent.getInputs()[0]
		tupleInput.isTuple() == true
		tupleInput.getComponents().isEmpty()
	}

	def "parseAbiContent should handle malformed object key path"() {
		given: "ABI JSON with malformed object key"
		def abiJson = '''{
			"address": "******************************************",
			"abi": []
		}'''
		def inputStream = new ByteArrayInputStream(abiJson.getBytes(StandardCharsets.UTF_8))
		def objectKey = "3000/"
		def lastModified = new Date()

		and: "Properties configured for non-truffle format"
		propertiesMock.getAbiFormat() >> "hardhat"

		when: "Parsing the ABI content"
		parser.parseAbiContent(inputStream, objectKey, lastModified)

		then: "Should throw ArrayIndexOutOfBoundsException"
		thrown(ArrayIndexOutOfBoundsException)
	}

	def "parseAbiContent should handle object key with no extension"() {
		given: "ABI JSON with object key without extension"
		def abiJson = '''{
			"address": "******************************************",
			"abi": []
		}'''
		def inputStream = new ByteArrayInputStream(abiJson.getBytes(StandardCharsets.UTF_8))
		def objectKey = "3000/TestContract"
		def lastModified = new Date()

		and: "Properties configured for non-truffle format"
		propertiesMock.getAbiFormat() >> "hardhat"

		when: "Parsing the ABI content"
		def result = parser.parseAbiContent(inputStream, objectKey, lastModified)

		then: "Should handle missing extension gracefully"
		result.address == "******************************************"
		result.name == "TestContract"
		result.lastModified == lastModified
	}

	def "findFirstAddressInNetworks should handle empty networks object"() {
		given: "Truffle ABI with empty networks"
		def truffleAbi = '''{
			"contractName": "TestContract",
			"networks": {},
			"abi": []
		}'''
		def inputStream = new ByteArrayInputStream(truffleAbi.getBytes(StandardCharsets.UTF_8))
		def objectKey = "3000/TestContract.json"
		def lastModified = new Date()

		and: "Properties configured for truffle format"
		propertiesMock.getAbiFormat() >> "truffle"

		when: "Parsing the ABI content"
		def result = parser.parseAbiContent(inputStream, objectKey, lastModified)

		then: "Should return empty address"
		result.address == ""
		result.name == "TestContract"
		result.lastModified == lastModified
	}

	def "findFirstAddressInNetworks should handle networks with no address field"() {
		given: "Truffle ABI with networks but no address field"
		def truffleAbi = '''{
			"contractName": "TestContract",
			"networks": {
				"1": {
					"transactionHash": "0x123",
					"blockNumber": 123
				}
			},
			"abi": []
		}'''
		def inputStream = new ByteArrayInputStream(truffleAbi.getBytes(StandardCharsets.UTF_8))
		def objectKey = "3000/TestContract.json"
		def lastModified = new Date()

		and: "Properties configured for truffle format"
		propertiesMock.getAbiFormat() >> "truffle"

		when: "Parsing the ABI content"
		def result = parser.parseAbiContent(inputStream, objectKey, lastModified)

		then: "Should return empty address"
		result.address == ""
		result.name == "TestContract"
		result.lastModified == lastModified
	}

	def "extractTupleComponents should handle reflection NoSuchMethodException"() {
		given: "ABI JSON with tuple type"
		def abiJson = '''[
			{
				"type": "event",
				"name": "ReflectionTestEvent",
				"inputs": [
					{
						"indexed": false,
						"name": "badTuple",
						"type": "tuple",
						"components": [
							{"name": "field1", "type": "uint256"}
						]
					}
				]
			}
		]'''

		when: "Parsing ABI that may trigger reflection exceptions"
		// The actual reflection exceptions are hard to trigger in normal parsing
		// but the code paths exist and are covered by the normal tuple processing
		def result = parser.parseAbi(abiJson)

		then: "Should handle gracefully"
		result.size() == 1
		def contractAbiEvent = result.values().iterator().next()
		contractAbiEvent.getEvent().getName() == "ReflectionTestEvent"
		contractAbiEvent.getInputs()[0].isTuple() == true
		contractAbiEvent.getInputs()[0].getComponents().size() == 1
	}

	def "extractTupleComponents should handle reflection IllegalAccessException"() {
		given: "ABI JSON with tuple that will cause IllegalAccessException"
		def abiJson = '''[
			{
				"type": "event",
				"name": "IllegalAccessEvent",
				"inputs": [
					{
						"indexed": false,
						"name": "restrictedTuple",
						"type": "tuple",
						"components": [
							{"name": "field1", "type": "address"}
						]
					}
				]
			}
		]'''

		when: "Parsing ABI that may trigger IllegalAccessException"
		def result = parser.parseAbi(abiJson)

		then: "Should handle gracefully"
		result.size() == 1
		def contractAbiEvent = result.values().iterator().next()
		contractAbiEvent.getEvent().getName() == "IllegalAccessEvent"
	}

	def "extractTupleComponents should handle reflection InvocationTargetException"() {
		given: "ABI JSON with tuple that may cause InvocationTargetException"
		def abiJson = '''[
			{
				"type": "event",
				"name": "InvocationTargetEvent",
				"inputs": [
					{
						"indexed": false,
						"name": "invocationTuple",
						"type": "tuple",
						"components": [
							{"name": "field1", "type": "bool"}
						]
					}
				]
			}
		]'''

		when: "Parsing ABI that may trigger InvocationTargetException"
		def result = parser.parseAbi(abiJson)

		then: "Should handle gracefully"
		result.size() == 1
		def contractAbiEvent = result.values().iterator().next()
		contractAbiEvent.getEvent().getName() == "InvocationTargetEvent"
	}

	def "extractTupleComponents should handle general exception"() {
		given: "ABI JSON with tuple that may cause general exception"
		def abiJson = '''[
			{
				"type": "event",
				"name": "GeneralExceptionEvent",
				"inputs": [
					{
						"indexed": false,
						"name": "exceptionTuple",
						"type": "tuple",
						"components": [
							{"name": "field1", "type": "string"}
						]
					}
				]
			}
		]'''

		when: "Parsing ABI that may trigger general exception"
		def result = parser.parseAbi(abiJson)

		then: "Should handle gracefully"
		result.size() == 1
		def contractAbiEvent = result.values().iterator().next()
		contractAbiEvent.getEvent().getName() == "GeneralExceptionEvent"
	}

	def "parseAbi should handle tuple with explicitly null components in JSON"() {
		given: "ABI JSON with explicitly null components"
		def abiJson = '''[
			{
				"type": "event",
				"name": "ExplicitNullEvent",
				"inputs": [
					{
						"indexed": false,
						"name": "nullComponentsTuple",
						"type": "tuple",
						"components": null
					}
				]
			}
		]'''

		when: "Parsing ABI with explicit null components"
		parser.parseAbi(abiJson)

		then: "Should throw IOException due to null components"
		thrown(IOException)
	}

	def "parseAbi should handle tuple with components containing unsupported nested type"() {
		given: "ABI JSON with tuple containing unsupported nested component type"
		def abiJson = '''[
			{
				"type": "event",
				"name": "UnsupportedNestedEvent",
				"inputs": [
					{
						"indexed": false,
						"name": "unsupportedNestedTuple",
						"type": "tuple",
						"components": [
							{"name": "field1", "type": "uint256"},
							{"name": "field2", "type": "unsupportedNestedType"}
						]
					}
				]
			}
		]'''

		when: "Parsing ABI with unsupported nested component type"
		parser.parseAbi(abiJson)

		then: "Should throw IOException due to unsupported type"
		thrown(IOException)
	}

	def "parseAbi should handle deeply nested tuple structures"() {
		given: "ABI JSON with deeply nested tuple structures"
		def abiJson = '''[
			{
				"type": "event",
				"name": "DeeplyNestedEvent",
				"inputs": [
					{
						"indexed": false,
						"name": "deepTuple",
						"type": "tuple",
						"components": [
							{"name": "level1", "type": "uint256"},
							{
								"name": "level2",
								"type": "tuple",
								"components": [
									{"name": "level2_field1", "type": "address"},
									{
										"name": "level3",
										"type": "tuple",
										"components": [
											{"name": "level3_field1", "type": "bool"},
											{"name": "level3_field2", "type": "string"}
										]
									}
								]
							}
						]
					}
				]
			}
		]'''

		when: "Parsing ABI with deeply nested tuples"
		def result = parser.parseAbi(abiJson)

		then: "Should handle deep nesting correctly"
		result.size() == 1
		def contractAbiEvent = result.values().iterator().next()
		contractAbiEvent.getEvent().getName() == "DeeplyNestedEvent"
		def deepTuple = contractAbiEvent.getInputs()[0]
		deepTuple.isTuple() == true
		deepTuple.getComponents().size() == 2

		// Check level 2 nesting
		def level2Tuple = deepTuple.getComponents()[1]
		level2Tuple.isTuple() == true
		level2Tuple.getComponents().size() == 2

		// Check level 3 nesting
		def level3Tuple = level2Tuple.getComponents()[1]
		level3Tuple.isTuple() == true
		level3Tuple.getComponents().size() == 2
	}

	def "parseAbi should handle tuple array with nested components"() {
		given: "ABI JSON with tuple array containing nested components"
		def abiJson = '''[
			{
				"type": "event",
				"name": "TupleArrayNestedEvent",
				"inputs": [
					{
						"indexed": false,
						"name": "tupleArrayNested",
						"type": "tuple[]",
						"components": [
							{"name": "field1", "type": "uint256"},
							{
								"name": "nestedTuple",
								"type": "tuple",
								"components": [
									{"name": "nestedField1", "type": "address"},
									{"name": "nestedField2", "type": "bytes32"}
								]
							}
						]
					}
				]
			}
		]'''

		when: "Parsing ABI with tuple array containing nested components"
		def result = parser.parseAbi(abiJson)

		then: "Should handle nested components in tuple array"
		result.size() == 1
		def contractAbiEvent = result.values().iterator().next()
		contractAbiEvent.getEvent().getName() == "TupleArrayNestedEvent"
		def tupleArray = contractAbiEvent.getInputs()[0]
		tupleArray.isTuple() == true
		tupleArray.getType() == "tuple[]"
		tupleArray.getComponents().size() == 2

		// Check nested tuple within array
		def nestedTuple = tupleArray.getComponents()[1]
		nestedTuple.isTuple() == true
		nestedTuple.getComponents().size() == 2
	}

	def "parseAbi should handle tuple with single component"() {
		given: "ABI JSON with tuple having single component"
		def abiJson = '''[
			{
				"type": "event",
				"name": "SingleComponentEvent",
				"inputs": [
					{
						"indexed": false,
						"name": "singleTuple",
						"type": "tuple",
						"components": [
							{"name": "onlyField", "type": "uint256"}
						]
					}
				]
			}
		]'''

		when: "Parsing ABI with single component tuple"
		def result = parser.parseAbi(abiJson)

		then: "Should handle single component correctly"
		result.size() == 1
		def contractAbiEvent = result.values().iterator().next()
		contractAbiEvent.getEvent().getName() == "SingleComponentEvent"
		def singleTuple = contractAbiEvent.getInputs()[0]
		singleTuple.isTuple() == true
		singleTuple.getComponents().size() == 1
		singleTuple.getComponents()[0].getName() == "onlyField"
		singleTuple.getComponents()[0].getType() == "uint256"
	}

	def "parseAbi should handle tuple with many components"() {
		given: "ABI JSON with tuple having many components"
		def abiJson = '''[
			{
				"type": "event",
				"name": "ManyComponentsEvent",
				"inputs": [
					{
						"indexed": false,
						"name": "manyTuple",
						"type": "tuple",
						"components": [
							{"name": "field1", "type": "uint256"},
							{"name": "field2", "type": "address"},
							{"name": "field3", "type": "bool"},
							{"name": "field4", "type": "string"},
							{"name": "field5", "type": "bytes32"},
							{"name": "field6", "type": "uint8"},
							{"name": "field7", "type": "int256"}
						]
					}
				]
			}
		]'''

		when: "Parsing ABI with many component tuple"
		def result = parser.parseAbi(abiJson)

		then: "Should handle many components correctly"
		result.size() == 1
		def contractAbiEvent = result.values().iterator().next()
		contractAbiEvent.getEvent().getName() == "ManyComponentsEvent"
		def manyTuple = contractAbiEvent.getInputs()[0]
		manyTuple.isTuple() == true
		manyTuple.getComponents().size() == 7
		manyTuple.getComponents()[0].getName() == "field1"
		manyTuple.getComponents()[6].getName() == "field7"
	}

	def "parseAbi should handle tuple with mixed indexed components"() {
		given: "ABI JSON with tuple having mixed indexed components"
		def abiJson = '''[
			{
				"type": "event",
				"name": "MixedIndexedEvent",
				"inputs": [
					{
						"indexed": true,
						"name": "indexedTuple",
						"type": "tuple",
						"components": [
							{"name": "field1", "type": "uint256", "indexed": true},
							{"name": "field2", "type": "address", "indexed": false}
						]
					}
				]
			}
		]'''

		when: "Parsing ABI with mixed indexed tuple"
		def result = parser.parseAbi(abiJson)

		then: "Should handle mixed indexed components correctly"
		result.size() == 1
		def contractAbiEvent = result.values().iterator().next()
		contractAbiEvent.getEvent().getName() == "MixedIndexedEvent"
		def indexedTuple = contractAbiEvent.getInputs()[0]
		indexedTuple.isTuple() == true
		indexedTuple.isIndexed() == true
		indexedTuple.getComponents().size() == 2
	}

	def "parseAbi should handle tuple with array components"() {
		given: "ABI JSON with tuple containing array components"
		def abiJson = '''[
			{
				"type": "event",
				"name": "TupleWithArraysEvent",
				"inputs": [
					{
						"indexed": false,
						"name": "tupleWithArrays",
						"type": "tuple",
						"components": [
							{"name": "field1", "type": "uint256[]"},
							{"name": "field2", "type": "address[]"},
							{"name": "field3", "type": "bytes32[5]"}
						]
					}
				]
			}
		]'''

		when: "Parsing ABI with tuple containing arrays"
		parser.parseAbi(abiJson)

		then: "Should throw IOException due to unsupported array types in components"
		thrown(IOException)
	}
}
