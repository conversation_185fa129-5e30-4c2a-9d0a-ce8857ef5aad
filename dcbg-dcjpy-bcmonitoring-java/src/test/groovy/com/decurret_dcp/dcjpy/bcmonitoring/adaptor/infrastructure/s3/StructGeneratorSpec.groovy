package com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3

import org.web3j.abi.datatypes.*
import org.web3j.abi.datatypes.generated.Uint256
import spock.lang.Specification
import java.math.BigInteger

class StructGeneratorSpec extends Specification {

    def "generateStructClass should create StaticStruct for static types"() {
        given: "A list of static field types"
        def fieldTypes = [
            Uint256.class,
            Address.class,
            Bool.class
        ]

        when: "Generating struct class"
        def structClass = StructGenerator.generateStructClass(fieldTypes)

        then: "Should create a StaticStruct subclass"
        structClass != null
        StaticStruct.class.isAssignableFrom(structClass)
        !DynamicStruct.class.isAssignableFrom(structClass)
    }

    def "generateStructClass should create DynamicStruct for dynamic types"() {
        given: "A list containing dynamic field types"
        def fieldTypes = [
            Uint256.class,
            Utf8String.class,  // Dynamic type
            Address.class
        ]

        when: "Generating struct class"
        def structClass = StructGenerator.generateStructClass(fieldTypes)

        then: "Should create a DynamicStruct subclass"
        structClass != null
        DynamicStruct.class.isAssignableFrom(structClass)
    }

    def "generateStructClass should create DynamicStruct when DynamicBytes is present"() {
        given: "A list containing DynamicBytes"
        def fieldTypes = [
            Uint256.class,
            DynamicBytes.class,  // Dynamic type
            Address.class
        ]

        when: "Generating struct class"
        def structClass = StructGenerator.generateStructClass(fieldTypes)

        then: "Should create a DynamicStruct subclass"
        structClass != null
        DynamicStruct.class.isAssignableFrom(structClass)
    }

    def "generateStructClass should create DynamicStruct when DynamicArray is present"() {
        given: "A list containing DynamicArray"
        def fieldTypes = [
            Uint256.class,
            DynamicArray.class,  // Dynamic type
            Address.class
        ]

        when: "Generating struct class"
        def structClass = StructGenerator.generateStructClass(fieldTypes)

        then: "Should create a DynamicStruct subclass"
        structClass != null
        DynamicStruct.class.isAssignableFrom(structClass)
    }

    def "generateStructClass should create DynamicStruct when DynamicStruct is present"() {
        given: "A list containing DynamicStruct"
        def fieldTypes = [
            Uint256.class,
            DynamicStruct.class,  // Dynamic type
            Address.class
        ]

        when: "Generating struct class"
        def structClass = StructGenerator.generateStructClass(fieldTypes)

        then: "Should create a DynamicStruct subclass"
        structClass != null
        DynamicStruct.class.isAssignableFrom(structClass)
    }

    def "generateStructClass should handle empty field types list"() {
        given: "An empty list of field types"
        def fieldTypes = []

        when: "Generating struct class"
        def structClass = StructGenerator.generateStructClass(fieldTypes)

        then: "Should create a StaticStruct subclass"
        structClass != null
        StaticStruct.class.isAssignableFrom(structClass)
    }

    def "generateStructClass should handle single field type"() {
        given: "A single field type"
        def fieldTypes = [Uint256.class]

        when: "Generating struct class"
        def structClass = StructGenerator.generateStructClass(fieldTypes)

        then: "Should create a StaticStruct subclass"
        structClass != null
        StaticStruct.class.isAssignableFrom(structClass)
    }

    def "generateStructClass should create unique class names"() {
        given: "Same field types"
        def fieldTypes = [Uint256.class, Address.class]

        when: "Generating multiple struct classes"
        def structClass1 = StructGenerator.generateStructClass(fieldTypes)
        def structClass2 = StructGenerator.generateStructClass(fieldTypes)

        then: "Should create different classes with unique names"
        structClass1 != null
        structClass2 != null
        structClass1 != structClass2
        structClass1.getName() != structClass2.getName()
    }

    def "isDynamic should return true for DynamicBytes"() {
        expect:
        StructGenerator.isDynamic(DynamicBytes.class) == true
    }

    def "isDynamic should return true for Utf8String"() {
        expect:
        StructGenerator.isDynamic(Utf8String.class) == true
    }

    def "isDynamic should return true for DynamicArray"() {
        expect:
        StructGenerator.isDynamic(DynamicArray.class) == true
    }

    def "isDynamic should return true for DynamicStruct"() {
        expect:
        StructGenerator.isDynamic(DynamicStruct.class) == true
    }

    def "isDynamic should return false for static types"() {
        expect:
        StructGenerator.isDynamic(Uint256.class) == false
        StructGenerator.isDynamic(Address.class) == false
        StructGenerator.isDynamic(Bool.class) == false
        StructGenerator.isDynamic(StaticStruct.class) == false
    }

    def "isDynamic should handle null parameter"() {
        when: "Calling isDynamic with null"
        def result = StructGenerator.isDynamic(null)

        then: "Should throw NullPointerException"
        thrown(NullPointerException)
    }

    def "generateStructClass should handle mixed static and dynamic types"() {
        given: "A mix of static and dynamic types"
        def fieldTypes = [
            Uint256.class,      // Static
            Address.class,      // Static
            Utf8String.class,   // Dynamic
            Bool.class          // Static
        ]

        when: "Generating struct class"
        def structClass = StructGenerator.generateStructClass(fieldTypes)

        then: "Should create a DynamicStruct subclass due to presence of dynamic type"
        structClass != null
        DynamicStruct.class.isAssignableFrom(structClass)
    }

    def "generateStructClass should create constructor with correct parameters"() {
        given: "Field types"
        def fieldTypes = [Uint256.class, Address.class]

        when: "Generating struct class"
        def structClass = StructGenerator.generateStructClass(fieldTypes)

        then: "Should have constructor with correct parameter types"
        structClass != null
        def constructors = structClass.getConstructors()
        constructors.length > 0

        def constructor = constructors.find { it.getParameterCount() == fieldTypes.size() }
        constructor != null
    }

    def "isDynamic should return true for subclasses of dynamic types"() {
        given: "Custom subclasses of dynamic types"
        // Create anonymous subclasses to test inheritance
        def customDynamicBytes = new DynamicBytes("test".bytes) {}.getClass()
        def customUtf8String = new Utf8String("test") {}.getClass()
        def customDynamicArray = new DynamicArray<Uint256>(Uint256.class, []) {}.getClass()
        def customDynamicStruct = new DynamicStruct([]) {}.getClass()

        expect: "All subclasses should be detected as dynamic"
        StructGenerator.isDynamic(customDynamicBytes) == true
        StructGenerator.isDynamic(customUtf8String) == true
        StructGenerator.isDynamic(customDynamicArray) == true
        StructGenerator.isDynamic(customDynamicStruct) == true
    }

    def "isDynamic should handle edge cases with inheritance"() {
        expect: "Should correctly identify dynamic types through inheritance"
        // Test with the exact classes to ensure all branches are covered
        StructGenerator.isDynamic(DynamicBytes.class) == true
        StructGenerator.isDynamic(Utf8String.class) == true
        StructGenerator.isDynamic(DynamicArray.class) == true
        StructGenerator.isDynamic(DynamicStruct.class) == true

        // Test with static types to ensure false path is covered
        StructGenerator.isDynamic(StaticStruct.class) == false
        StructGenerator.isDynamic(Uint256.class) == false
    }

    def "isDynamic should cover all branch combinations"() {
        expect: "Should test all logical branches in the OR chain"
        // Test cases that will reach each specific condition

        // This should hit the first condition (DynamicBytes) and return true
        StructGenerator.isDynamic(DynamicBytes.class) == true

        // This should skip DynamicBytes (false) but hit Utf8String (true)
        StructGenerator.isDynamic(Utf8String.class) == true

        // This should skip DynamicBytes and Utf8String (both false) but hit DynamicArray (true)
        StructGenerator.isDynamic(DynamicArray.class) == true

        // This should skip first three (all false) but hit DynamicStruct (true)
        StructGenerator.isDynamic(DynamicStruct.class) == true

        // This should skip all four conditions (all false) and return false
        // This is the key test case to cover the missing branch
        StructGenerator.isDynamic(StaticStruct.class) == false
        StructGenerator.isDynamic(Uint256.class) == false
        StructGenerator.isDynamic(Address.class) == false
        StructGenerator.isDynamic(Bool.class) == false

        // Test with a class that's not related to any of the dynamic types
        StructGenerator.isDynamic(String.class) == false
        StructGenerator.isDynamic(Object.class) == false
    }

    def "isDynamic should achieve 100% branch coverage"() {
        expect: "All branches in the OR chain should be covered"
        // Test each condition individually to ensure all branches are hit

        // Test the true branches
        StructGenerator.isDynamic(DynamicBytes.class) == true
        StructGenerator.isDynamic(Utf8String.class) == true
        StructGenerator.isDynamic(DynamicArray.class) == true
        StructGenerator.isDynamic(DynamicStruct.class) == true

        // Test the false branches - classes that don't inherit from any dynamic type
        StructGenerator.isDynamic(StaticStruct.class) == false
        StructGenerator.isDynamic(Uint256.class) == false
        StructGenerator.isDynamic(Address.class) == false
        StructGenerator.isDynamic(Bool.class) == false

        // Test with completely unrelated classes to ensure the final condition returns false
        StructGenerator.isDynamic(Integer.class) == false
        StructGenerator.isDynamic(List.class) == false
        StructGenerator.isDynamic(Map.class) == false
    }

    def "constructor should be callable for completeness"() {
        when: "Creating an instance of StructGenerator"
        def generator = new StructGenerator()

        then: "Should create instance successfully"
        generator != null
        generator instanceof StructGenerator
    }

    def "generateStructClass should handle exception scenarios"() {
        when: "Generating struct class with null list"
        StructGenerator.generateStructClass(null)

        then: "Should throw NullPointerException"
        thrown(NullPointerException)
    }

    def "generateStructClass should create fields correctly"() {
        given: "Multiple field types"
        def fieldTypes = [Uint256.class, Address.class, Bool.class]

        when: "Generating struct class"
        def structClass = StructGenerator.generateStructClass(fieldTypes)

        then: "Should create class with correct number of fields"
        structClass != null
        def fields = structClass.getDeclaredFields()
        // Filter out synthetic fields and only count our defined fields
        def userFields = fields.findAll { !it.isSynthetic() && it.name.startsWith("field") }
        userFields.size() == fieldTypes.size()

        // Verify field names
        userFields.eachWithIndex { field, index ->
            assert field.name == "field${index}"
        }
    }

    def "generateStructClass should handle large number of fields"() {
        given: "Many field types"
        def fieldTypes = (1..10).collect { Uint256.class }

        when: "Generating struct class with many fields"
        def structClass = StructGenerator.generateStructClass(fieldTypes)

        then: "Should create class successfully"
        structClass != null
        StaticStruct.class.isAssignableFrom(structClass)
    }

    def "generateStructClass should create instantiable classes"() {
        given: "Field types"
        def fieldTypes = [Uint256.class, Address.class]

        when: "Generating and instantiating struct class"
        def structClass = StructGenerator.generateStructClass(fieldTypes)
        def constructor = structClass.getConstructor(Uint256.class, Address.class)
        def instance = constructor.newInstance(
            new Uint256(BigInteger.valueOf(123)),
            new Address("0x1234567890123456789012345678901234567890")
        )

        then: "Should create working instance"
        instance != null
        StaticStruct.class.isAssignableFrom(instance.getClass())
    }
}
